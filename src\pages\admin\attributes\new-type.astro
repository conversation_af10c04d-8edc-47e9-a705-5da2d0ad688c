---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import Card from '../../../components/ui/Card.astro';
import CardHeader from '../../../components/ui/CardHeader.astro';
import CardTitle from '../../../components/ui/CardTitle.astro';
import CardContent from '../../../components/ui/CardContent.astro';
import Button from '../../../components/ui/Button.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

const isNewType = true;
---

<AdminLayout title="Создание нового типа атрибута | LuxBeton">
  <div class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Header Section -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Создание нового типа атрибута</h1>
          <p class="mt-2 text-sm text-gray-600">Настройка структуры и полей для нового типа атрибута</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
          <Button href="/admin/attributes" variant="secondary" class="w-full sm:w-auto">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Назад к списку
          </Button>
        </div>
      </div>
    </div>

    <!-- Form -->
    <Card>
      <CardHeader>
        <CardTitle>Конфигурация нового типа атрибута</CardTitle>
      </CardHeader>
      <CardContent>
        <form id="attribute-type-form" class="space-y-6">
          <!-- Основные настройки -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="typeKey" class="block text-sm font-medium text-gray-700 mb-2">
                Ключ типа <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="typeKey"
                name="typeKey"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="my_custom_attribute"
                pattern="^[a-z_]+$"
                title="Только строчные буквы и подчеркивания"
                required
              />
              <p class="mt-1 text-xs text-gray-500">Используется в коде. Только строчные буквы и подчеркивания.</p>
            </div>

            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Название <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Мой пользовательский атрибут"
                required
              />
              <p class="mt-1 text-xs text-gray-500">Отображаемое название типа атрибута.</p>
            </div>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Описание
            </label>
            <textarea
              id="description"
              name="description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Описание назначения этого типа атрибута"
            ></textarea>
          </div>

          <div>
            <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
              Иконка
            </label>
            <input
              type="text"
              id="icon"
              name="icon"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="tag, star, settings, etc."
            />
            <p class="mt-1 text-xs text-gray-500">Название иконки для отображения в интерфейсе.</p>
          </div>

          <!-- Специальные настройки -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="flex items-center">
              <input
                type="checkbox"
                id="isSimpleArray"
                name="isSimpleArray"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="isSimpleArray" class="ml-2 block text-sm text-gray-700">
                Простой массив строк
              </label>
            </div>

            <div class="flex items-center">
              <input
                type="checkbox"
                id="isGrouped"
                name="isGrouped"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="isGrouped" class="ml-2 block text-sm text-gray-700">
                Группированные данные
              </label>
            </div>
          </div>

          <!-- Настройки отображения атрибутов -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Настройки отображения атрибутов</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="showOnProductPage"
                  name="showOnProductPage"
                  checked
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="showOnProductPage" class="ml-2 block text-sm text-gray-700">
                  Показывать на странице товара
                </label>
              </div>

              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="showInProductCard"
                  name="showInProductCard"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="showInProductCard" class="ml-2 block text-sm text-gray-700">
                  Показывать в карточке товара
                </label>
              </div>
            </div>
            <p class="mt-2 text-xs text-gray-500">
              Управляйте тем, где будут отображаться атрибуты этого типа: на детальной странице товара и/или в карточках товаров в каталоге.
            </p>
          </div>

          <!-- Поля атрибута -->
          <div>
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Поля атрибута</h3>
              <button
                type="button"
                id="add-field-btn"
                class="add-field-btn inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                style="background-color: #3b82f6;"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Добавить поле
              </button>
            </div>

            <div id="fields-container" class="space-y-4">
              <!-- Поля будут добавлены через JavaScript -->
            </div>
          </div>

          <!-- Настройки отображения -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Настройки отображения</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="listViewFields" class="block text-sm font-medium text-gray-700 mb-2">
                  Поля для списка
                </label>
                <input
                  type="text"
                  id="listViewFields"
                  name="listViewFields"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="name, description"
                />
                <p class="mt-1 text-xs text-gray-500">Поля для отображения в списке (через запятую).</p>
              </div>

              <div>
                <label for="cardViewFields" class="block text-sm font-medium text-gray-700 mb-2">
                  Поля для карточек
                </label>
                <input
                  type="text"
                  id="cardViewFields"
                  name="cardViewFields"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="name, description"
                />
                <p class="mt-1 text-xs text-gray-500">Поля для отображения в карточках (через запятую).</p>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div>
                <label for="colorField" class="block text-sm font-medium text-gray-700 mb-2">
                  Поле цвета
                </label>
                <input
                  type="text"
                  id="colorField"
                  name="colorField"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="hex"
                />
                <p class="mt-1 text-xs text-gray-500">Поле для отображения цвета (если есть).</p>
              </div>

              <div>
                <label for="format" class="block text-sm font-medium text-gray-700 mb-2">
                  Формат отображения
                </label>
                <input
                  type="text"
                  id="format"
                  name="format"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="{field1} - {field2}"
                />
                <p class="mt-1 text-xs text-gray-500">Шаблон для форматирования значений.</p>
              </div>
            </div>
          </div>

          <!-- Кнопки действий -->
          <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
            <button
              type="submit"
              class="create-type-submit-btn w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              style="background-color: #3b82f6;"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Создать тип атрибута
            </button>

            <Button href="/admin/attributes" variant="secondary" class="w-full sm:w-auto">
              Отмена
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>

  <!-- Уведомления -->
  <div id="notification" class="fixed top-4 right-4 z-50 hidden">
    <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
      <div class="flex items-center">
        <div id="notification-icon" class="flex-shrink-0 mr-3"></div>
        <div id="notification-message" class="text-sm text-gray-700"></div>
      </div>
    </div>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки создания типа атрибута */
  .create-type-submit-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки добавления поля */
  .add-field-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script define:vars={{ isNewType }}>
  // Глобальные переменные
  let fieldCounter = 0;

  // Инициализация
  document.addEventListener('DOMContentLoaded', function() {
    initializeFields();
    setupEventListeners();
  });

  // Инициализация полей
  function initializeFields() {
    // Добавляем одно пустое поле по умолчанию
    addFieldToDOM({
      key: '',
      name: '',
      type: 'string',
      required: false
    }, 0);
  }

  // Настройка обработчиков событий
  function setupEventListeners() {
    // Добавление нового поля
    document.getElementById('add-field-btn').addEventListener('click', function() {
      const newField = {
        key: '',
        name: '',
        type: 'string',
        required: false
      };
      addFieldToDOM(newField, fieldCounter++);
    });

    // Отправка формы
    document.getElementById('attribute-type-form').addEventListener('submit', handleFormSubmit);

    // Автоматическое заполнение ключа на основе названия
    document.getElementById('name').addEventListener('input', function(e) {
      const typeKeyInput = document.getElementById('typeKey');
      if (!typeKeyInput.value) {
        const key = e.target.value
          .toLowerCase()
          .replace(/[^а-яa-z0-9\s]/g, '')
          .replace(/\s+/g, '_')
          .replace(/[^a-z0-9_]/g, '');
        typeKeyInput.value = key;
      }
    });
  }

  // Добавление поля в DOM
  function addFieldToDOM(field, index) {
    const fieldsContainer = document.getElementById('fields-container');

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'border border-gray-200 rounded-lg p-4 bg-gray-50';
    fieldDiv.dataset.fieldIndex = index;

    fieldDiv.innerHTML = `
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-900">Поле ${index + 1}</h4>
        <button type="button" class="remove-field-btn text-red-600 hover:text-red-800">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Ключ</label>
          <input
            type="text"
            name="field_key_${index}"
            value="${field.key || ''}"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="id, name, etc."
            required
          />
        </div>

        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Название</label>
          <input
            type="text"
            name="field_name_${index}"
            value="${field.name || ''}"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="ID, Название, etc."
            required
          />
        </div>

        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Тип</label>
          <select
            name="field_type_${index}"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="string" ${field.type === 'string' ? 'selected' : ''}>Строка</option>
            <option value="number" ${field.type === 'number' ? 'selected' : ''}>Число</option>
            <option value="text" ${field.type === 'text' ? 'selected' : ''}>Текст</option>
            <option value="color" ${field.type === 'color' ? 'selected' : ''}>Цвет</option>
            <option value="boolean" ${field.type === 'boolean' ? 'selected' : ''}>Булево</option>
            <option value="select" ${field.type === 'select' ? 'selected' : ''}>Выбор</option>
          </select>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              name="field_required_${index}"
              ${field.required ? 'checked' : ''}
              class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span class="ml-1 text-xs text-gray-700">Обязательное</span>
          </label>

          <label class="flex items-center">
            <input
              type="checkbox"
              name="field_unique_${index}"
              ${field.unique ? 'checked' : ''}
              class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span class="ml-1 text-xs text-gray-700">Уникальное</span>
          </label>
        </div>

        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Валидация (JSON)</label>
          <textarea
            name="field_validation_${index}"
            rows="2"
            class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder='{"pattern": "^[a-z]+$", "message": "Только строчные буквы"}'
          >${field.validation ? JSON.stringify(field.validation, null, 2) : ''}</textarea>
        </div>
      </div>
    `;

    // Добавляем обработчик удаления поля
    const removeBtn = fieldDiv.querySelector('.remove-field-btn');
    removeBtn.addEventListener('click', function() {
      if (fieldsContainer.children.length > 1) {
        fieldDiv.remove();
      } else {
        window.adminModal?.showError('Должно остаться хотя бы одно поле');
      }
    });

    fieldsContainer.appendChild(fieldDiv);
    fieldCounter = Math.max(fieldCounter, index + 1);
  }

  // Обработка отправки формы
  async function handleFormSubmit(e) {
    e.preventDefault();

    try {
      const formData = new FormData(e.target);
      const config = buildConfigFromForm(formData);

      const response = await fetch('/api/admin/attribute-types-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          typeKey: formData.get('typeKey'),
          config: config
        })
      });

      if (response.ok) {
        await window.adminModal?.showSuccess('Тип атрибута успешно создан');
        setTimeout(() => {
          window.location.href = '/admin/attributes';
        }, 1500);
      } else {
        const error = await response.json();
        await window.adminModal?.showError('Ошибка: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      await window.adminModal?.showError('Ошибка при создании');
    }
  }

  // Построение конфигурации из формы
  function buildConfigFromForm(formData) {
    const config = {
      name: formData.get('name'),
      description: formData.get('description') || '',
      icon: formData.get('icon') || '',
      isSimpleArray: formData.get('isSimpleArray') === 'on',
      isGrouped: formData.get('isGrouped') === 'on',
      showOnProductPage: formData.get('showOnProductPage') === 'on',
      showInProductCard: formData.get('showInProductCard') === 'on',
      fields: [],
      display: {
        listView: formData.get('listViewFields') ? formData.get('listViewFields').split(',').map(s => s.trim()).filter(s => s) : [],
        cardView: formData.get('cardViewFields') ? formData.get('cardViewFields').split(',').map(s => s.trim()).filter(s => s) : [],
        colorField: formData.get('colorField') || '',
        format: formData.get('format') || ''
      }
    };

    // Собираем поля
    const fieldsContainer = document.getElementById('fields-container');
    const fieldDivs = fieldsContainer.querySelectorAll('[data-field-index]');

    fieldDivs.forEach((fieldDiv, index) => {
      const fieldKey = formData.get(`field_key_${fieldDiv.dataset.fieldIndex}`);
      const fieldName = formData.get(`field_name_${fieldDiv.dataset.fieldIndex}`);
      const fieldType = formData.get(`field_type_${fieldDiv.dataset.fieldIndex}`);
      const fieldRequired = formData.get(`field_required_${fieldDiv.dataset.fieldIndex}`) === 'on';
      const fieldUnique = formData.get(`field_unique_${fieldDiv.dataset.fieldIndex}`) === 'on';
      const fieldValidation = formData.get(`field_validation_${fieldDiv.dataset.fieldIndex}`);

      if (fieldKey && fieldName) {
        const field = {
          key: fieldKey,
          name: fieldName,
          type: fieldType,
          required: fieldRequired,
          unique: fieldUnique
        };

        if (fieldValidation) {
          try {
            field.validation = JSON.parse(fieldValidation);
          } catch (e) {
            console.warn('Неверный JSON в валидации поля:', fieldKey);
          }
        }

        config.fields.push(field);
      }
    });

    return config;
  }

  // Показ уведомлений
  function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const icon = document.getElementById('notification-icon');
    const messageEl = document.getElementById('notification-message');

    messageEl.textContent = message;

    // Устанавливаем иконку в зависимости от типа
    if (type === 'success') {
      icon.innerHTML = '<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
    } else if (type === 'error') {
      icon.innerHTML = '<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
    } else {
      icon.innerHTML = '<svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
    }

    notification.classList.remove('hidden');

    // Автоматически скрываем через 5 секунд
    setTimeout(() => {
      notification.classList.add('hidden');
    }, 5000);
  }
</script>

<style>
  /* Стили для формы */
  .container {
    max-width: 1200px;
  }

  /* Анимации для уведомлений */
  #notification {
    transition: all 0.3s ease-in-out;
  }

  #notification.hidden {
    opacity: 0;
    transform: translateX(100%);
  }

  #notification:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
  }
</style>
