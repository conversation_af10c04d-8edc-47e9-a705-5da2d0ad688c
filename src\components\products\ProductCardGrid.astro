---
import type { Product } from '../../types';
import Tooltip from '../ui/Tooltip.astro';

interface Props {
  product: Product;
  attributeTypesConfig?: any;
  'data-size'?: string;
  'data-color'?: string;
  'data-price'?: string | number;
  'data-name'?: string;
}

const { product, attributeTypesConfig = {} } = Astro.props;

// Prepare size and color display
const mainSizeValue = product.attributes?.size?.length
  ? `${product.attributes.size.length} x ${product.attributes.size.width} x ${product.attributes.size.height} мм`
  : product.attributes?.size?.variants?.[0]
    ? `${product.attributes.size.variants[0].length} x ${product.attributes.size.variants[0].width} x ${product.attributes.size.variants[0].height} мм`
    : null;

const mainColor = product.attributes?.colors?.[0] || null;
const colorPigments = product.attributes?.color_pigments;

// Prepare color display with pigments and actual colors
const getColorDisplay = () => {
  if (!colorPigments) return mainColor;

  const colors = product.attributes?.colors || [];
  if (colors.length === 0) return colorPigments.name;

  // Determine how many colors to show based on pigment type
  let colorsToShow: string[] = [];
  if (colorPigments.id === 'no_pigment') {
    colorsToShow = [colors[0]]; // Just show the main color (usually gray)
  } else if (colorPigments.id === 'one_pigment') {
    colorsToShow = colors.slice(0, 1); // Show 1 color
  } else if (colorPigments.id === 'two_pigments') {
    colorsToShow = colors.slice(0, 2); // Show 2 colors
  } else if (colorPigments.id === 'three_or_more_pigments') {
    colorsToShow = colors.slice(0, 3); // Show 3 colors
  }

  if (colorsToShow.length === 0) {
    return colorPigments.name;
  }

  return `${colorPigments.name} (${colorsToShow.join(', ')})`;
};

const colorDisplay = getColorDisplay();
---

<div class="product-card bg-white shadow-md rounded-none overflow-visible group flex flex-col"
     data-category={product.category}
     data-size={Astro.props['data-size']}
     data-color={Astro.props['data-color']}
     data-price={Astro.props['data-price']}
     data-name={Astro.props['data-name']}
     data-subcategory={product.subcategory.toLowerCase().replace(/\s+/g, '-')}>
  {/* Image Section (clickable) */}
  <a href={`/products/${product.categorySlug}/${product.slug}`} class="relative overflow-hidden flex-shrink-0 w-full aspect-square block">
    <img
      src={`/product/${product.images.main}`}
      alt={product.name}
      class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
    />
  </a>

  {/* Details Section (Grid View) */}
  <div class="p-2 sm:p-3 lg:p-3 xl:p-3 2xl:p-4 flex-grow flex flex-col">
    {/* Top row: Name */}
    <div class="mb-1">
      <h3 class="text-base lg:text-base xl:text-base 2xl:text-lg font-bold text-text-main line-clamp-2">
        <a href={`/products/${product.categorySlug}/${product.slug}`} class="hover:underline">{product.name}</a>
      </h3>
    </div>

    {/* Article */}
    <p class="text-gray-600 text-xs lg:text-xs xl:text-[12px] 2xl:text-sm mt-1 mb-1">Арт: {product.id}</p>

    {/* Size */}
    {mainSizeValue && attributeTypesConfig.standard_sizes?.showInProductCard !== false && (
      <p class="text-gray-700 text-xs lg:text-xs xl:text-[13px] 2xl:text-sm mt-1 mb-1">
        <span class="font-semibold">Размер:</span> {mainSizeValue}
      </p>
    )}

    {/* Color */}
    {(mainColor || colorPigments) && (
      (!colorPigments && mainColor && attributeTypesConfig.colors?.showInProductCard !== false) ||
      (colorPigments && attributeTypesConfig.color_pigments?.showInProductCard !== false)
    ) && (
      <p class="text-gray-700 text-xs lg:text-xs xl:text-[13px] 2xl:text-sm mt-1 mb-1">
        <span class="font-semibold">Цвет:</span> {colorDisplay}
        {colorPigments && (
          <Tooltip
            content={colorPigments.description}
            position="top"
            size="sm"
            triggerClass="ml-1"
          />
        )}
      </p>
    )}

    {/* Price */}
    <p class="text-[#baa385] text-base lg:text-base xl:text-lg 2xl:text-xl font-semibold mb-2 lg:mb-2 mt-auto text-right">
      {product.price.value.toFixed(2)} руб. / {product.price.unit}
    </p>

    {/* Buttons */}
    <div class="flex space-x-1 lg:space-x-1 xl:space-x-2 2xl:space-x-2 mt-2 lg:mt-2">
      <a href={`/products/${product.categorySlug}/${product.slug}`} class="flex-1 border border-[#baa385] bg-white text-[#baa385] font-semibold py-2.5 sm:py-2.5 lg:py-2 xl:py-2 2xl:py-2.5 text-xs lg:text-xs xl:text-sm 2xl:text-sm text-center hover:bg-gray-50 transition-colors">Подробнее</a>
      <a href="/request" class="flex-1 bg-[#baa385] text-white font-semibold py-2.5 sm:py-2.5 lg:py-2 xl:py-2 2xl:py-2.5 text-xs lg:text-xs xl:text-sm 2xl:text-sm text-center hover:bg-[#a89274] transition-colors">Заказать</a>
    </div>
  </div>
</div>
