# Документация по управлению товарами LuxBeton

Этот документ содержит полную информацию о системе управления товарами, включая структуру данных, правила именования изображений и автоматическую генерацию ID товаров.

## Структура проекта

```
/data/product/
├── products.json         # Основной файл с данными о товарах
├── categories.json       # Файл с категориями товаров
├── schema.md            # Документация по структуре данных
└── integration-example.js # Пример интеграции

/public/product/         # Директория с изображениями товаров
├── TB-001/             # Папка с изображениями товара TB-001
│   ├── trotuarnaya-plitka-klassika_main.jpg    # Главное изображение
│   ├── trotuarnaya-plitka-klassika_1.jpg       # Дополнительное изображение 1
│   ├── trotuarnaya-plitka-klassika_2.jpg       # Дополнительное изображение 2
│   └── trotuarnaya-plitka-klassika_3.jpg       # Дополнительное изображение 3
├── BR-001/             # Папка с изображениями товара BR-001
│   ├── klever-krakovskij_main.jpg              # Главное изображение
│   └── klever-krakovskij_1.jpg                 # Дополнительное изображение 1
└── ...                 # Папки других товаров
```

## 🚀 Автоматическая генерация ID товаров

### Система генерации ID

Проект использует автоматическую систему генерации уникальных ID товаров через админ-панель:

**Формат ID:** `КАТЕГОРИЯ-ХХХ`

**Примеры:**
- `TB-001`, `TB-002`, `TB-003` - Тротуарная плитка
- `BR-001`, `BR-002`, `BR-003` - Брусчатка
- `BD-001`, `BD-002` - Бордюры
- `VS-001` - Водостоки
- `ST-001` - Ступени
- `ZB-001` - Заборы
- `MF-001` - Малые архитектурные формы
- `FP-001` - Фасадные панели

### Категории и их ID

| Категория | ID | Описание |
|-----------|----|---------|
| Тротуарная плитка | TB | Trotuarnaya plitka |
| Брусчатка | BR | BRuschatka |
| Бордюры | BD | BorDyury |
| Водостоки | VS | VodoStoki |
| Ступени | ST | STupeni |
| Заборы | ZB | ZaBory |
| Малые архитектурные формы | MF | Malye Formy |
| Фасадные панели | FP | Fasadnye Paneli |

### Процесс создания товара через админ-панель

1. **Выбор категории** → система автоматически генерирует уникальный ID
2. **Заполнение данных** товара (название, описание, характеристики)
3. **Загрузка изображений** (автоматическое именование по новым правилам)
4. **Сохранение** → автоматическое создание папки `/public/product/ID/`

### Текущая статистика товаров

- **TB (Тротуарная плитка):** 5 товаров, следующий ID: `TB-006`
- **BR (Брусчатка):** 2 товара, следующий ID: `BR-004`
- **BD (Бордюры):** 1 товар, следующий ID: `BD-002`
- **VS (Водостоки):** 0 товаров, следующий ID: `VS-001`
- **ST (Ступени):** 1 товар, следующий ID: `ST-002`
- **ZB (Заборы):** 1 товар, следующий ID: `ZB-002`
- **MF (Малые архитектурные формы):** 0 товаров, следующий ID: `MF-001`
- **FP (Фасадные панели):** 1 товар, следующий ID: `FP-002`

### API для генерации ID

```javascript
// Генерация ID для конкретной категории
GET /api/admin/generate-product-id?category=Брусчатка
// Ответ: { "success": true, "productId": "BR-004", "category": "Брусчатка" }

// Получение статистики по всем категориям
GET /api/admin/generate-product-id?stats=true
// Ответ: { "success": true, "stats": { ... } }
```

## 📸 Правила именования изображений

### Новая система именования (текущая)

**Главное изображение:**
- Формат: `описательное-название_main.jpg/png`
- Примеры: `plitka-crewz_main.png`, `bruschatka-elit_main.jpg`

**Дополнительные изображения:**
- Формат: `описательное-название_1.jpg/png`, `описательное-название_2.jpg/png` и т.д.
- Примеры: `plitka-crewz_1.png`, `bruschatka-elit_2.jpg`

### Примеры правильного именования

```
TB-001/
├── trotuarnaya-plitka-klassika_main.jpg     # Главное изображение
├── trotuarnaya-plitka-klassika_1.jpg        # Дополнительное изображение 1
├── trotuarnaya-plitka-klassika_2.jpg        # Дополнительное изображение 2
└── trotuarnaya-plitka-klassika_3.jpg        # Дополнительное изображение 3

BR-003/
├── bruschatka-kamennyj-cvetok-elit_main.jpg # Главное изображение
├── bruschatka-kamennyj-cvetok-elit_1.jpg    # Дополнительное изображение 1
└── bruschatka-kamennyj-cvetok-elit_2.jpg    # Дополнительное изображение 2
```

### Автоматическое определение изображений

Система автоматически определяет изображения товара по следующим правилам:
- **Главное изображение**: ищется файл, заканчивающийся на `_main`
- **Дополнительные изображения**: ищутся файлы, заканчивающиеся на `_1`, `_2`, `_3` и т.д.

### Процесс добавления изображений

1. **Создайте папку** с именем, соответствующим ID товара (например, `TB-001`)
2. **Подготовьте и оптимизируйте изображения** согласно требованиям к размеру и качеству
3. **Именуйте файлы** согласно новым правилам:
   - Главное изображение: `описательное-название_main.jpg/png`
   - Дополнительные: `описательное-название_1.jpg/png`, `описательное-название_2.jpg/png` и т.д.
4. **Разместите изображения** в созданной папке
5. **Обновите данные товара** в админ-панели или используйте автоматическое определение

### Поддерживаемые форматы

- **JPG/JPEG** - рекомендуется для фотографий
- **PNG** - рекомендуется для изображений с прозрачностью
- **Максимальное количество**: 1 главное + до 10 дополнительных изображений

## 🔧 Работа с данными

### Добавление нового товара

**Через админ-панель (рекомендуется):**
1. Перейдите в `/admin/products/new`
2. Выберите категорию → ID генерируется автоматически
3. Заполните данные товара
4. Загрузите изображения → папка создается автоматически
5. Сохраните товар

**Вручную:**
1. Создать новую запись в файле `products.json` с уникальным ID
2. Создать папку для изображений товара в `/public/product/ID/`
3. Добавить изображения товара с правильными именами

### Обновление информации о товаре

**Через админ-панель:**
- Перейдите в `/admin/products/edit/ID`
- Используйте кнопку "Обновить изображения" для автоматического определения

**Вручную:**
- Отредактируйте соответствующую запись в файле `products.json`

### Удаление товара

1. Удалить соответствующую запись из файла `products.json`
2. Удалить папку с изображениями товара из `/public/product/`

## 💻 Интеграция с фронтендом

### Загрузка данных в Astro

```javascript
// Загрузка всех товаров
import productsData from '../data/product/products.json';

// Загрузка категорий
import categoriesData from '../data/product/categories.json';

// Фильтрация товаров по категории
const categoryProducts = productsData.filter(product =>
  product.categorySlug === 'bruschatka'
);
```

### Отображение изображений

```astro
---
// В компоненте Astro
const product = productsData[0];
---

<!-- Главное изображение -->
<img src={`/product/${product.images.main}`} alt={product.name} />

<!-- Дополнительные изображения -->
{product.images.additional.map(img => (
  <img src={`/product/${img}`} alt={product.name} />
))}
```

### API endpoints

```javascript
// Получение всех товаров
GET /api/products

// Получение товара по ID
GET /api/products/TB-001

// Генерация ID товара (админ)
GET /api/admin/generate-product-id?category=Брусчатка

// Автоматическое определение изображений (админ)
GET /api/admin/detect-images?productId=TB-001
```

## 📊 Утилиты и инструменты

### Утилиты для работы с изображениями

**Файл:** `src/utils/imageUtils.js`

```javascript
import { autoDetectProductImages, generateImageFileName } from '../utils/imageUtils.js';

// Автоматическое определение изображений товара
const images = await autoDetectProductImages('TB-001');

// Генерация имени файла изображения
const fileName = generateImageFileName('TB-001', 'Тротуарная плитка', true);
```

### Утилиты для генерации ID

**Файл:** `src/utils/productIdGenerator.js`

```javascript
import { generateProductId, getCategoryStats } from '../utils/productIdGenerator.js';

// Генерация ID товара
const productId = await generateProductId('Брусчатка');

// Получение статистики категорий
const stats = await getCategoryStats();
```

### Страница статистики

Доступна по адресу `/admin/categories-stats` - показывает:
- Количество товаров в каждой категории
- Последний использованный номер
- Следующий доступный ID
- Общую статистику проекта

## 📋 Структура данных товара

```json
{
  "id": "TB-001",
  "name": "Тротуарная плитка «Классика»",
  "category": "Тротуарная плитка",
  "categorySlug": "trotuarnaya-plitka",
  "subcategory": "Классическая серия",
  "shortDescription": "Классическая тротуарная плитка",
  "fullDescription": "Подробное описание товара...",
  "price": {
    "value": 450,
    "unit": "м²"
  },
  "images": {
    "main": "TB-001/trotuarnaya-plitka-klassika_main.jpg",
    "additional": [
      "TB-001/trotuarnaya-plitka-klassika_1.jpg",
      "TB-001/trotuarnaya-plitka-klassika_2.jpg"
    ]
  },
  "specifications": {
    "colors": ["Серый", "Красный"],
    "texture": "Гладкая",
    "dimensions": {
      "length": 300,
      "width": 300,
      "height": 60
    },
    "weight": 25,
    "strength": "B25"
  },
  "inStock": true
}
```

## � Безопасность и лучшие практики

### Аутентификация админ-панели

- Все API endpoints для администрирования защищены аутентификацией
- Доступ к админ-панели требует входа через `/admin/login`
- Автоматическое перенаправление неавторизованных пользователей

### Рекомендации по работе с изображениями

1. **Оптимизация размера:** рекомендуется сжимать изображения перед загрузкой
2. **Резервное копирование:** регулярно создавайте бэкапы папки `/public/product/`
3. **Именование файлов:** используйте только латинские символы, цифры и дефисы
4. **Максимальный размер:** ограничьте размер файлов до 2MB для оптимальной производительности

### Работа с данными

1. **Резервное копирование:** регулярно создавайте бэкапы `products.json` и `categories.json`
2. **Валидация данных:** проверяйте корректность данных перед сохранением
3. **Уникальность ID:** система автоматически обеспечивает уникальность, но при ручном редактировании будьте осторожны
4. **Консистентность:** используйте автоматические инструменты для поддержания целостности данных

## 🚀 Производительность

### Оптимизация изображений

- Используйте современные форматы (WebP, AVIF) для лучшего сжатия
- Создавайте несколько размеров изображений для адаптивной загрузки
- Применяйте lazy loading для изображений товаров

### Кэширование

- Настройте кэширование статических файлов изображений
- Используйте CDN для быстрой доставки изображений
- Кэшируйте данные товаров на стороне клиента

## 🔧 Устранение неполадок

### Частые проблемы

**Товар не отображается в категории:**
- Проверьте правильность `categorySlug` в данных товара
- Убедитесь, что категория существует в `categories.json`

**Изображения не загружаются:**
- Проверьте правильность путей в объекте `images`
- Убедитесь, что файлы существуют в папке `/public/product/`
- Используйте кнопку "Обновить изображения" в админ-панели

**Ошибка генерации ID:**
- Проверьте, что категория существует в `categories.json`
- Убедитесь в корректности данных в `products.json`

### Логи и отладка

- Проверяйте консоль браузера для ошибок JavaScript
- Используйте Network tab для отслеживания запросов к API
- Проверяйте логи сервера для ошибок на стороне бэкенда

## �🔍 Дополнительная информация

- **Схема данных:** `schema.md` - подробное описание структуры данных
- **Пример интеграции:** `integration-example.js` - примеры использования API
- **Админ-панель:** `/admin/products` - управление товарами
- **Статистика:** `/admin/categories-stats` - статистика по категориям

## 🏷️ Управление типами атрибутов

### Обзор системы атрибутов

Система атрибутов LuxBeton позволяет создавать и управлять различными характеристиками товаров с полной настройкой структуры данных, валидации и отображения.

### Структура файлов атрибутов

```
/data/product/
├── attributes.json              # Основные данные атрибутов
├── attribute-types-config.json  # Конфигурация типов атрибутов
└── README.md                   # Документация
```

### Предустановленные типы атрибутов

| Тип | Ключ | Описание | Поля |
|-----|------|----------|------|
| **Цвет** | `colors` | Цветовые характеристики | id, name, hex |
| **Текстура** | `textures` | Текстурные характеристики | value (строка) |
| **Класс прочности** | `strength_classes` | Классы прочности бетона | class, description |
| **Морозостойкость** | `frost_resistance` | Характеристики морозостойкости | class, description |
| **Водопоглощение** | `water_absorption` | Характеристики водопоглощения | class, description |
| **Размер** | `standard_sizes` | Стандартные размеры | length, width, height |
| **Поверхность** | `surfaces` | Типы поверхностей | id, name, description |
| **Рисунок** | `patterns` | Рисунки и узоры | id, name, description |
| **Цветовые пигменты** | `color_pigments` | Количество пигментов | id, name, description |

### Создание нового типа атрибута

**Через админ-панель (рекомендуется):**

1. **Перейдите** в `/admin/attributes`
2. **Нажмите** кнопку "Добавить" → "Создать новый тип атрибута"
3. **Заполните основные параметры:**
   - **Ключ типа**: `my_custom_attribute` (только строчные буквы и подчеркивания)
   - **Название**: `Мой пользовательский атрибут`
   - **Описание**: Описание назначения атрибута
   - **Иконка**: Название иконки для интерфейса

4. **Настройте специальные параметры:**
   - ☑️ **Простой массив строк** - для простых списков значений
   - ☑️ **Группированные данные** - для сложных структур с группировкой

5. **Добавьте поля атрибута:**
   - **Ключ поля**: `name`, `value`, `id` и т.д.
   - **Название поля**: Отображаемое название в формах
   - **Тип данных**: строка, число, текст, цвет, булево, выбор
   - **Обязательность**: ☑️ Обязательное поле
   - **Уникальность**: ☑️ Уникальные значения
   - **Валидация**: JSON с правилами валидации

6. **Настройте отображение:**
   - **Поля для списка**: `name, description` (через запятую)
   - **Поля для карточек**: `name, description`
   - **Поле цвета**: `hex` (если есть цветовое поле)
   - **Формат отображения**: `{name} - {description}`

### Редактирование существующего типа атрибута

**Через админ-панель:**

1. **Перейдите** в `/admin/attributes`
2. **Выберите** нужный тип атрибута
3. **Нажмите** кнопку "Редактировать тип"
4. **Измените** любые параметры:
   - Основные настройки (название, описание, иконка)
   - Структуру полей (добавить/удалить/изменить поля)
   - Настройки отображения
   - Валидацию полей

### Структура конфигурации типа атрибута

```json
{
  "my_custom_type": {
    "name": "Мой пользовательский тип",
    "description": "Описание назначения этого типа атрибута",
    "icon": "tag",
    "isSimpleArray": false,
    "isGrouped": false,
    "fields": [
      {
        "key": "id",
        "name": "ID",
        "type": "string",
        "required": true,
        "unique": true,
        "validation": {
          "pattern": "^[a-z_]+$",
          "message": "ID должен содержать только строчные буквы и подчеркивания"
        }
      },
      {
        "key": "name",
        "name": "Название",
        "type": "string",
        "required": true,
        "validation": {
          "minLength": 1,
          "maxLength": 100
        }
      }
    ],
    "display": {
      "listView": ["name", "description"],
      "cardView": ["name", "description"],
      "colorField": "",
      "format": "{name}"
    }
  }
}
```

### Типы полей атрибутов

| Тип | Описание | Примеры использования |
|-----|----------|----------------------|
| **string** | Короткая строка | ID, название, код |
| **number** | Числовое значение | Размеры, вес, количество |
| **text** | Длинный текст | Описания, комментарии |
| **color** | Цветовое значение | Hex-коды цветов |
| **boolean** | Булево значение | Флаги, переключатели |
| **select** | Выбор из списка | Предустановленные варианты |

### Валидация полей

**Примеры правил валидации:**

```json
{
  "pattern": "^[A-Z][0-9]+(\\.[0-9]+)?$",
  "message": "Класс должен начинаться с буквы и содержать числа",
  "minLength": 1,
  "maxLength": 50,
  "min": 0,
  "max": 10000
}
```

**Доступные правила:**
- `pattern` - регулярное выражение
- `message` - сообщение об ошибке
- `minLength` / `maxLength` - ограничения длины строки
- `min` / `max` - ограничения числовых значений

### Специальные типы атрибутов

**Простой массив строк (`isSimpleArray: true`):**
```json
{
  "textures": [
    "шероховатая",
    "рельефная",
    "состаренная"
  ]
}
```

**Группированные данные (`isGrouped: true`):**
```json
{
  "sizes": {
    "pavers": [
      { "length": 200, "width": 100, "height": 40 },
      { "length": 300, "width": 300, "height": 60 }
    ],
    "curbs": [
      { "length": 500, "width": 50, "height": 200 }
    ]
  }
}
```

### API для управления типами атрибутов

**Получение конфигурации типов:**
```javascript
GET /api/admin/attribute-types-config
```

**Создание нового типа:**
```javascript
POST /api/admin/attribute-types-config
{
  "typeKey": "my_custom_type",
  "config": { /* конфигурация типа */ }
}
```

**Обновление типа:**
```javascript
PUT /api/admin/attribute-types-config
{
  "typeKey": "my_custom_type",
  "config": { /* обновленная конфигурация */ },
  "oldTypeKey": "old_type_key" // если ключ изменился
}
```

**Удаление типа:**
```javascript
DELETE /api/admin/attribute-types-config
{
  "typeKey": "my_custom_type"
}
```

### Интеграция с товарами

**Использование атрибутов в товарах:**

```json
{
  "id": "TB-001",
  "name": "Тротуарная плитка «Классика»",
  "specifications": {
    "colors": ["gray", "red"],
    "textures": ["гладкая"],
    "strength_classes": ["B25"],
    "frost_resistance": ["F200"],
    "my_custom_attribute": ["custom_value_1", "custom_value_2"]
  }
}
```

**Загрузка атрибутов в компонентах:**

```javascript
// Загрузка данных атрибутов
import attributesData from '../data/product/attributes.json';
import attributeTypesConfig from '../data/product/attribute-types-config.json';

// Получение конфигурации типа
const colorConfig = attributeTypesConfig.colors;

// Получение значений атрибута
const availableColors = attributesData.colors;
```

### Лучшие практики

**При создании новых типов атрибутов:**

1. **Используйте описательные ключи** - `material_type` вместо `mt`
2. **Добавляйте валидацию** для обеспечения качества данных
3. **Настраивайте отображение** для удобства работы в админке
4. **Документируйте назначение** в поле описания

**При работе с полями:**

1. **Делайте ключевые поля обязательными** (`required: true`)
2. **Используйте уникальность** для идентификаторов (`unique: true`)
3. **Добавляйте понятные сообщения** в валидацию
4. **Выбирайте подходящие типы данных** для каждого поля

**При настройке отображения:**

1. **Указывайте важные поля** в `listView` и `cardView`
2. **Используйте форматирование** для сложных значений
3. **Настраивайте цветовые поля** для визуального отображения

### Устранение неполадок

**Атрибут не отображается в админке:**
- Проверьте правильность ключа типа в `attribute-types-config.json`
- Убедитесь, что тип существует в `attributes.json`

**Ошибка валидации при сохранении:**
- Проверьте правильность JSON в поле валидации
- Убедитесь, что все обязательные поля заполнены

**Значения атрибута не сохраняются:**
- Проверьте соответствие структуры данных конфигурации типа
- Убедитесь в правильности типов полей

### Миграция существующих атрибутов

При обновлении структуры существующих типов атрибутов:

1. **Создайте резервную копию** `attributes.json`
2. **Обновите конфигурацию** в `attribute-types-config.json`
3. **Проверьте совместимость** существующих данных
4. **При необходимости обновите** данные в `attributes.json`

## ⚙️ Настройки отображения атрибутов

### Обзор настроек

Система позволяет настраивать порядок отображения и внешний вид типов атрибутов в админ-панели.

### Доступ к настройкам

**Через админ-панель:**
1. Перейдите в `/admin/attributes`
2. Нажмите кнопку "Настройки" в правом верхнем углу
3. В модальном окне настройте параметры отображения

### Настраиваемые параметры

| Параметр | Описание | Значения |
|----------|----------|----------|
| **Порядок вкладок** | Последовательность отображения типов атрибутов | Drag & Drop сортировка |
| **Количество основных вкладок** | Сколько вкладок показывать до выпадающего меню | 4-8 вкладок |
| **Показывать счетчики** | Отображение количества элементов в каждом типе | Вкл/Выкл |
| **Компактный режим** | Уменьшенные отступы и размеры элементов | Вкл/Выкл |

### Структура файла настроек

```json
{
  "tabOrder": [
    "colors",
    "textures",
    "strength_classes",
    "frost_resistance",
    "water_absorption",
    "standard_sizes",
    "surfaces",
    "patterns",
    "color_pigments"
  ],
  "visibleTabsCount": 6,
  "showCounters": true,
  "compactMode": false,
  "lastUpdated": "2024-12-25T00:00:00.000Z"
}
```

### API для управления настройками

**Получение настроек:**
```javascript
GET /api/admin/attributes-display-settings
```

**Обновление настроек:**
```javascript
PUT /api/admin/attributes-display-settings
{
  "tabOrder": ["colors", "textures", ...],
  "visibleTabsCount": 6,
  "showCounters": true,
  "compactMode": false
}
```

**Сброс к настройкам по умолчанию:**
```javascript
POST /api/admin/attributes-display-settings
```

### Файлы настроек

```
/data/admin/
└── attributes-display-settings.json  # Настройки отображения атрибутов
```

### Применение настроек

**Порядок вкладок:**
- Первые N типов (по настройке `visibleTabsCount`) отображаются как основные вкладки
- Остальные типы доступны через выпадающее меню "Еще"
- Порядок определяется массивом `tabOrder`

**Счетчики:**
- При включении показывают количество элементов в каждом типе: "Цвет (11)"
- При выключении показывают только название: "Цвет"

**Компактный режим:**
- Уменьшает отступы и размеры шрифтов
- Применяется CSS класс `.compact-mode` к body
- Подходит для работы на небольших экранах

### Сброс настроек

**Через интерфейс:**
1. Откройте настройки отображения
2. Нажмите кнопку "Сбросить"
3. Подтвердите действие

**Через API:**
```javascript
fetch('/api/admin/attributes-display-settings', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' }
})
```

### Лучшие практики

**При настройке порядка:**
1. **Размещайте часто используемые типы** в начале списка
2. **Группируйте связанные типы** рядом друг с другом
3. **Учитывайте рабочий процесс** команды при выборе порядка

**При выборе количества вкладок:**
1. **На больших экранах** можно использовать 7-8 вкладок
2. **На средних экранах** оптимально 5-6 вкладок
3. **Для мобильных устройств** рекомендуется 4-5 вкладок

**При использовании компактного режима:**
1. **Включайте для работы** на планшетах и небольших ноутбуках
2. **Выключайте на больших мониторах** для лучшей читаемости
3. **Тестируйте удобство** работы с интерфейсом

---

**Версия документации:** 2.2
**Последнее обновление:** Декабрь 2024
**Автор:** Система управления товарами LuxBeton
