{"textures": {"name": "Текстура", "description": "Текстурные характеристики поверхности", "icon": "texture", "isSimpleArray": true, "isGrouped": false, "showOnProductPage": true, "showInProductCard": true, "fields": [{"key": "value", "name": "Название текстуры", "type": "string", "required": true, "unique": false, "validation": {"minLength": 1, "maxLength": 100}}], "display": {"listView": ["value"], "cardView": ["value"], "colorField": "", "format": ""}}, "strength_classes": {"name": "Класс прочности", "description": "Классы прочности бетонных изделий", "icon": "shield", "isSimpleArray": false, "isGrouped": false, "showOnProductPage": true, "showInProductCard": true, "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[A-Z][0-9]+(\\.[0-9]+)?$", "message": "Класс должен начинаться с буквы и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "unique": false, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"], "colorField": "", "format": ""}}, "frost_resistance": {"name": "Морозостойкость", "description": "Характеристики морозостойкости изделий", "icon": "snowflake", "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^F[0-9]+$", "message": "Класс морозостойкости должен начинаться с F и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"]}, "showOnProductPage": true, "showInProductCard": false}, "water_absorption": {"name": "Водопоглощение", "description": "Характеристики водопоглощения изделий", "icon": "droplet", "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^W[0-9]+$", "message": "Класс водопоглощения должен начинаться с W и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"]}, "showOnProductPage": true, "showInProductCard": false}, "sizes": {"name": "Размер", "description": "Стандартные размеры изделий", "icon": "ruler", "isSimpleArray": false, "isGrouped": true, "showOnProductPage": false, "showInProductCard": false, "fields": [{"key": "length", "name": "<PERSON><PERSON>ина (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}, {"key": "width", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}, {"key": "height", "name": "Высота (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}], "display": {"listView": ["length", "width", "height"], "cardView": ["length", "width", "height"], "colorField": "", "format": "{length}×{width}×{height} мм"}}, "surfaces": {"name": "Поверхность", "description": "Типы поверхностей изделий", "icon": "layers", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"]}, "showOnProductPage": true, "showInProductCard": false}, "patterns": {"name": "Рисунок", "description": "Рисунки и узоры на изделиях", "icon": "pattern", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"]}, "showOnProductPage": false, "showInProductCard": true}, "color_pigments": {"name": "Цветовые пигменты", "description": "Количество цветовых пигментов в изделии", "icon": "palette", "isSimpleArray": false, "isGrouped": false, "showOnProductPage": true, "showInProductCard": false, "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "unique": false, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "unique": false, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"], "colorField": "", "format": ""}}, "colors": {"name": "Цвет", "description": "Цветовые характеристики изделий", "icon": "palette", "isSimpleArray": false, "isGrouped": false, "showOnProductPage": false, "showInProductCard": false, "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "unique": false, "validation": {"minLength": 1, "maxLength": 50}}, {"key": "hex", "name": "Hex код", "type": "color", "required": true, "unique": false, "validation": {"pattern": "^#[0-9A-Fa-f]{6}$", "message": "Неверный формат hex-кода цвета"}}], "display": {"listView": ["name", "hex"], "cardView": ["name", "hex"], "colorField": "hex", "format": ""}}, "weight": {"name": "<PERSON>е<PERSON>", "description": "Вес изделия с указанием единицы измерения", "icon": "scale", "fields": [{"key": "value", "name": "Значение", "type": "number", "required": true, "validation": {"min": 0.01, "max": 10000}}, {"key": "unit", "name": "Единица измерения", "type": "select", "required": true, "options": [{"value": "г", "label": "грамм"}, {"value": "кг", "label": "килогра<PERSON>м"}], "validation": {"enum": ["г", "кг"]}}], "display": {"listView": ["value", "unit"], "cardView": ["value", "unit"], "format": "{value} {unit}"}, "showOnProductPage": true, "showInProductCard": false}, "material": {"name": "Материал", "description": "Материал изготовления изделия", "icon": "layers", "isSimpleArray": false, "isGrouped": false, "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": false, "validation": {"maxLength": 500}}], "display": {"listView": ["name"], "cardView": ["name", "description"], "colorField": "", "format": "{name}"}, "showOnProductPage": true, "showInProductCard": false}, "tip_pokrytiya": {"name": "Тип покрытия", "description": "Типы покрытия для пола", "icon": "tag", "isSimpleArray": false, "isGrouped": false, "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": false, "validation": {"maxLength": 500}}], "display": {"listView": ["name"], "cardView": ["name", "description"], "colorField": "", "format": "{name}"}, "showOnProductPage": true, "showInProductCard": false}}